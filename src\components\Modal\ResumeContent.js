"use client";

import React from 'react';
import <PERSON><PERSON> from '../Button';

const ResumeContent = () => {
  const handleDownloadPDF = () => {
    // TODO: Implement PDF download functionality
    console.log('Download PDF clicked');
  };

  return (
    <div className="p-8 pr-16">
      {/* Header with Picture Space */}
      <div className="mb-6 relative">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h1 className="text-3xl font-heading font-bold text-secondary mb-2">
              Teod<PERSON>-<PERSON><PERSON><PERSON>
            </h1>
            <p className="text-xl text-secondary/80 mb-3">
              Multimedia Artist & Creative Professional
            </p>
            <Button
              onClick={handleDownloadPDF}
              variant="filled"
              size="small"
            >
              Download PDF
            </Button>
          </div>

          {/* Picture placeholder - will be styled as background with fade */}
          <div className="w-32 h-32 ml-6 rounded-lg bg-secondary/5 border border-secondary/10 flex items-center justify-center">
            <span className="text-secondary/40 text-sm">Photo</span>
            {/* Future: Background image with fade effect will go here */}
          </div>
        </div>
      </div>

      {/* Professional Summary */}
      <div className="mb-8">
        <p className="text-secondary/80 leading-relaxed">
          I'm a versatile multimedia artist with experience across digital entertainment, marketing, and creative production. My work blends graphic design, 3D rendering, branding, and motion graphics, supported by an efficient, adaptable workflow. I move seamlessly between tools and disciplines to deliver production-ready assets with speed and precision. Guided by curiosity and continuous learning, I've also explored game design and interactive media to expand my creative range.
        </p>
      </div>

      {/* Resume Content */}
      <div className="space-y-8">
        {/* Experience Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Experience
          </h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-secondary">Video & Animation Designer</h3>
              <p className="text-secondary/80 font-medium">vidaXL.com</p>
              <p className="text-secondary/70 text-sm mb-2">November 2022 - Present • Bucharest</p>
              <p className="text-secondary/70 text-sm">
                vidaXL is an international online retailer, with headquarters in Venlo, The Netherlands.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-secondary">Graphic Designer</h3>
              <p className="text-secondary/80 font-medium">vidaXL.com</p>
              <p className="text-secondary/70 text-sm">September 2021 - October 2022 • Bucharest</p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-secondary">Graphic Designer</h3>
              <p className="text-secondary/80 font-medium">Sound Design Theatre Company</p>
              <p className="text-secondary/70 text-sm mb-2">September 2019 - January 2021</p>
              <p className="text-secondary/70 text-sm">
                Sound Design Theatre Company focuses on the production of musical shows in Romania.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-secondary">Graphic Designer</h3>
              <p className="text-secondary/80 font-medium">Newton Coin Project</p>
              <p className="text-secondary/70 text-sm mb-2">June 2018 - December 2018 • Remote</p>
              <p className="text-secondary/70 text-sm">
                Cryptocurrency created to help fund research in the medical and renewable energy fields.
              </p>
            </div>
          </div>
        </section>

        {/* Education Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Education
          </h2>
          <div>
            <h3 className="text-lg font-semibold text-secondary">Animation & Game Design</h3>
            <p className="text-secondary/80 font-medium">SAE Institute (Media Academy)</p>
            <p className="text-secondary/70 text-sm mb-3">October 2016 - September 2017 • Bucharest</p>
            <ul className="text-secondary/70 space-y-1 text-sm">
              <li>• Game design theory, level design, and character design</li>
              <li>• Classic and digital drawing fundamentals</li>
              <li>• 3D software proficiency: 3ds Max, Autodesk Maya, ZBrush</li>
              <li>• Procedural texturing, digital environment design, and animation basics</li>
            </ul>
          </div>
        </section>

        {/* Skills Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Skills
          </h2>
          <div className="space-y-6">
            {/* Tech Stack */}
            <div>
              <h3 className="text-lg font-semibold text-secondary mb-3">Tech Stack</h3>
              <div className="flex flex-wrap gap-3 justify-start">
                {[
                  {
                    name: 'Adobe Photoshop',
                    icon: '/Creative Software Icons/Photoshop-512.png',
                    alt: 'Adobe Photoshop'
                  },
                  {
                    name: 'Adobe Illustrator',
                    icon: '/Creative Software Icons/Illustrator-512.png',
                    alt: 'Adobe Illustrator'
                  },
                  {
                    name: 'Adobe InDesign',
                    icon: '/Creative Software Icons/InDesign-512.png',
                    alt: 'Adobe InDesign'
                  },
                  {
                    name: 'Adobe After Effects',
                    icon: '/Creative Software Icons/After Effects-512.png',
                    alt: 'Adobe After Effects'
                  },
                  {
                    name: 'Adobe Lightroom',
                    icon: '/Creative Software Icons/Lightroom-512.png',
                    alt: 'Adobe Lightroom'
                  },
                  {
                    name: 'Blender',
                    icon: '/Creative Software Icons/blender-icon.svg',
                    alt: 'Blender'
                  },
                  {
                    name: 'Unreal Engine 5',
                    iconLight: '/Creative Software Icons/UE-Icon-2023-Black.svg',
                    iconDark: '/Creative Software Icons/UE-Icon-2023-White.svg',
                    alt: 'Unreal Engine 5'
                  },
                  {
                    name: 'VS Code',
                    icon: '/Creative Software Icons/vscode.svg',
                    alt: 'Visual Studio Code'
                  },
                  {
                    name: 'GPT (AI)',
                    iconLight: '/Creative Software Icons/OpenAI-black-monoblossom.svg',
                    iconDark: '/Creative Software Icons/OpenAI-white-monoblossom.svg',
                    alt: 'OpenAI GPT'
                  }
                ].map((software, index) => (
                  <div key={index} className="flex items-center justify-center">
                    {software.icon ? (
                      <img
                        src={software.icon}
                        alt={software.alt}
                        className="w-8 h-8 object-contain"
                        title={software.name}
                      />
                    ) : (
                      <>
                        <img
                          src={software.iconLight}
                          alt={software.alt}
                          className="w-8 h-8 object-contain [html[data-theme='light']_&]:block [html[data-theme='dark']_&]:hidden"
                          title={software.name}
                        />
                        <img
                          src={software.iconDark}
                          alt={software.alt}
                          className="w-8 h-8 object-contain [html[data-theme='light']_&]:hidden [html[data-theme='dark']_&]:block"
                          title={software.name}
                        />
                      </>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Professional Skills & Languages */}
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-secondary mb-3">Professional Skills</h3>
                <ul className="text-secondary/80 space-y-1">
                  <li>• Collaboration</li>
                  <li>• Adaptability</li>
                  <li>• Problem-Solving</li>
                  <li>• Communication</li>
                  <li>• Organization</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-secondary mb-3">Languages</h3>
                <ul className="text-secondary/80 space-y-1">
                  <li>• English (Full Professional Proficiency)</li>
                  <li>• Romanian (Native)</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Interests Section */}
        <section>
          <h2 className="text-2xl font-heading font-bold text-secondary mb-4 border-b border-secondary/20 pb-2">
            Interests
          </h2>
          <div className="flex flex-wrap gap-3">
            {[
              'Graphic Design',
              'Music Composition',
              'Animation',
              'Scriptwriting',
              'Level Design'
            ].map((interest, index) => (
              <span
                key={index}
                className="bg-accent/10 px-3 py-1 rounded-full text-sm font-medium [html[data-theme='light']_&]:text-secondary [html[data-theme='dark']_&]:text-accent"
              >
                {interest}
              </span>
            ))}
          </div>
        </section>
      </div>
    </div>
  );
};

export default ResumeContent;
