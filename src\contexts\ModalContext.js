"use client";

import React, { createContext, useContext, useState } from 'react';

const ModalContext = createContext();

export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

export const ModalProvider = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [modalType, setModalType] = useState(null);
  const [modalData, setModalData] = useState(null);

  const openModal = (type, data = null) => {
    setModalType(type);
    setModalData(data);
    setIsOpen(true);

    // Store current scroll position
    const scrollY = window.scrollY;
    document.documentElement.style.setProperty('--scroll-y', `${scrollY}px`);

    // Prevent body scroll when modal is open - scrollbar stays visible
    document.body.classList.add('modal-open');
    document.body.style.top = `-${scrollY}px`;
  };

  const closeModal = () => {
    setIsOpen(false);

    // Get stored scroll position
    const scrollY = document.documentElement.style.getPropertyValue('--scroll-y');

    // Restore body scroll
    document.body.classList.remove('modal-open');
    document.body.style.top = '';

    // Restore scroll position
    if (scrollY) {
      window.scrollTo(0, parseInt(scrollY));
    }

    // Clean up CSS custom property
    document.documentElement.style.removeProperty('--scroll-y');

    // Delay clearing modal type/data until after animation completes
    setTimeout(() => {
      setModalType(null);
      setModalData(null);
    }, 250); // Slightly longer than the 200ms animation
  };

  return (
    <ModalContext.Provider value={{
      isOpen,
      modalType,
      modalData,
      openModal,
      closeModal
    }}>
      {children}
    </ModalContext.Provider>
  );
};
