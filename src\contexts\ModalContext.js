"use client";

import React, { createContext, useContext, useState } from 'react';

const ModalContext = createContext();

export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

export const ModalProvider = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [modalType, setModalType] = useState(null);
  const [modalData, setModalData] = useState(null);

  const openModal = (type, data = null) => {
    setModalType(type);
    setModalData(data);
    setIsOpen(true);

    // Prevent body scroll when modal is open - scrollbar stays visible
    document.body.classList.add('modal-open');
  };

  const closeModal = () => {
    setIsOpen(false);

    // Delay cleanup until after modal animation completes
    setTimeout(() => {
      // Restore body scroll
      document.body.classList.remove('modal-open');

      // Clear modal type/data
      setModalType(null);
      setModalData(null);
    }, 250); // Wait for the 200ms animation to complete
  };

  return (
    <ModalContext.Provider value={{
      isOpen,
      modalType,
      modalData,
      openModal,
      closeModal
    }}>
      {children}
    </ModalContext.Provider>
  );
};
